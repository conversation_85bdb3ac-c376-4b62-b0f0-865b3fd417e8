// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package ui

import (
	"fmt"
	"io"
	"time"

	"github.com/briandowns/spinner"
	"github.com/schollz/progressbar/v3"
)

// Spinner wraps the spinner functionality.
type Spinner struct {
	spinner *spinner.Spinner
	writer  io.Writer
}

// NewSpinner creates a new spinner with the given message.
func NewSpinner(writer io.Writer, message string) *Spinner {
	s := spinner.New(spinner.CharSets[14], 100*time.Millisecond)
	s.Writer = writer
	s.Suffix = fmt.Sprintf(" %s", message)
	_ = s.Color("cyan")

	return &Spinner{
		spinner: s,
		writer:  writer,
	}
}

// Start starts the spinner.
func (s *Spinner) Start() {
	s.spinner.Start()
}

// Stop stops the spinner.
func (s *Spinner) Stop() {
	s.spinner.Stop()
}

// UpdateMessage updates the spinner message.
func (s *Spinner) UpdateMessage(message string) {
	s.spinner.Suffix = fmt.Sprintf(" %s", message)
}

// SuccessAndStop stops the spinner and shows success message.
func (s *Spinner) SuccessAndStop(message string) {
	s.spinner.Stop()
	fmt.Fprintf(s.writer, "%s\n", Success(message))
}

// ErrorAndStop stops the spinner and shows error message.
func (s *Spinner) ErrorAndStop(message string) {
	s.spinner.Stop()
	fmt.Fprintf(s.writer, "%s\n", Error(message))
}

// ProgressBar wraps the progress bar functionality.
type ProgressBar struct {
	bar    *progressbar.ProgressBar
	writer io.Writer
}

// NewProgressBar creates a new progress bar.
func NewProgressBar(writer io.Writer, maxValue int, description string) *ProgressBar {
	bar := progressbar.NewOptions(maxValue,
		progressbar.OptionSetWriter(writer),
		progressbar.OptionSetDescription(description),
		progressbar.OptionSetTheme(progressbar.Theme{
			Saucer:        "=",
			SaucerHead:    ">",
			SaucerPadding: " ",
			BarStart:      "[",
			BarEnd:        "]",
		}),
		progressbar.OptionShowCount(),
		progressbar.OptionShowIts(),
		progressbar.OptionSetWidth(50),
		progressbar.OptionThrottle(65*time.Millisecond),
		progressbar.OptionShowElapsedTimeOnFinish(),
		progressbar.OptionSetRenderBlankState(true),
	)

	return &ProgressBar{
		bar:    bar,
		writer: writer,
	}
}

// Add increments the progress bar.
func (p *ProgressBar) Add(num int) error {
	return p.bar.Add(num)
}

// Set sets the progress bar to a specific value.
func (p *ProgressBar) Set(num int) error {
	return p.bar.Set(num)
}

// Finish completes the progress bar.
func (p *ProgressBar) Finish() error {
	return p.bar.Finish()
}

// Clear clears the progress bar.
func (p *ProgressBar) Clear() error {
	return p.bar.Clear()
}

// Describe updates the description.
func (p *ProgressBar) Describe(description string) {
	p.bar.Describe(description)
}

// StepProgress represents a step-based progress indicator.
type StepProgress struct {
	steps       []string
	current     int
	failed      bool
	writer      io.Writer
	title       string
	startTime   time.Time
	stepSpinner *Spinner
}

// NewStepProgress creates a new step progress indicator.
func NewStepProgress(writer io.Writer, title string, steps []string) *StepProgress {
	return &StepProgress{
		steps:   steps,
		current: 0,
		writer:  writer,
		title:   title,
	}
}

// Start starts the step progress.
func (sp *StepProgress) Start() {
	sp.startTime = time.Now()
	fmt.Fprintf(sp.writer, "\n")
	fmt.Fprintf(sp.writer, "%s\n", Bold(sp.title))
	fmt.Fprintf(sp.writer, "%s\n\n", Dim("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"))
	sp.printOverview()
	fmt.Fprintf(sp.writer, "\n")
	sp.startCurrentStep()
}

// NextStep moves to the next step and marks current as complete.
func (sp *StepProgress) NextStep() {
	if sp.stepSpinner != nil {
		sp.stepSpinner.SuccessAndStop(fmt.Sprintf("Step %d/%d completed", sp.current+1, len(sp.steps)))
	}

	if sp.current < len(sp.steps) {
		sp.current++
		if sp.current < len(sp.steps) {
			sp.startCurrentStep()
		}
	}
}

// FailCurrentStep marks the current step as failed.
func (sp *StepProgress) FailCurrentStep() {
	sp.failed = true
	if sp.stepSpinner != nil {
		sp.stepSpinner.ErrorAndStop(fmt.Sprintf("Step %d/%d failed", sp.current+1, len(sp.steps)))
	}
	sp.printSummary()
}

// Complete marks all steps as complete.
func (sp *StepProgress) Complete() {
	if sp.stepSpinner != nil {
		sp.stepSpinner.SuccessAndStop(fmt.Sprintf("Step %d/%d completed", sp.current+1, len(sp.steps)))
	}
	sp.current = len(sp.steps)
	sp.printSummary()
}

// startCurrentStep starts the spinner for the current step.
func (sp *StepProgress) startCurrentStep() {
	if sp.current < len(sp.steps) {
		stepMsg := fmt.Sprintf("[%d/%d] %s", sp.current+1, len(sp.steps), sp.steps[sp.current])
		sp.stepSpinner = NewSpinner(sp.writer, stepMsg)
		sp.stepSpinner.Start()
	}
}

// printOverview prints an overview of all steps.
func (sp *StepProgress) printOverview() {
	fmt.Fprintf(sp.writer, "%s Steps Overview:\n", Info("📋"))
	for i, step := range sp.steps {
		fmt.Fprintf(sp.writer, "   %s %d. %s\n", Dim("•"), i+1, Dim(step))
	}
}

// printSummary prints the final summary.
func (sp *StepProgress) printSummary() {
	elapsed := time.Since(sp.startTime)
	fmt.Fprintf(sp.writer, "\n")
	fmt.Fprintf(sp.writer, "%s\n", Dim("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"))

	if sp.failed {
		fmt.Fprintf(sp.writer, "%s Process failed after %v\n", Error("❌"), elapsed.Round(time.Second))
		fmt.Fprintf(sp.writer, "%s Failed at step %d: %s\n", Error(""), sp.current+1, sp.steps[sp.current])
	} else {
		fmt.Fprintf(sp.writer, "%s Process completed successfully in %v\n", Success("✅"), elapsed.Round(time.Second))
		fmt.Fprintf(sp.writer, "%s All %d steps completed\n", Success(""), len(sp.steps))
	}
	fmt.Fprintf(sp.writer, "\n")
}

// DetailedStepProgress represents a detailed step-based progress indicator with sub-steps.
type DetailedStepProgress struct {
	mainSteps   []string
	currentMain int
	subSteps    []string
	currentSub  int
	failed      bool
	writer      io.Writer
	title       string
	startTime   time.Time
	mainSpinner *Spinner
	subSpinner  *Spinner
}

// NewDetailedStepProgress creates a new detailed step progress indicator.
func NewDetailedStepProgress(writer io.Writer, title string, mainSteps []string) *DetailedStepProgress {
	return &DetailedStepProgress{
		mainSteps:   mainSteps,
		currentMain: 0,
		writer:      writer,
		title:       title,
	}
}

// StartMainStep starts a main step with optional sub-steps.
func (dsp *DetailedStepProgress) StartMainStep(subSteps []string) {
	if dsp.currentMain == 0 {
		dsp.startTime = time.Now()
		dsp.printHeader()
	}

	if dsp.mainSpinner != nil {
		dsp.mainSpinner.SuccessAndStop(fmt.Sprintf("Step %d/%d completed", dsp.currentMain, len(dsp.mainSteps)))
	}

	if dsp.currentMain < len(dsp.mainSteps) {
		dsp.subSteps = subSteps
		dsp.currentSub = 0

		stepMsg := fmt.Sprintf("[%d/%d] %s", dsp.currentMain+1, len(dsp.mainSteps), dsp.mainSteps[dsp.currentMain])
		fmt.Fprintf(dsp.writer, "\n%s %s\n", Bold("▶"), Bold(stepMsg))

		if len(subSteps) > 0 {
			dsp.printSubStepsOverview()
			dsp.startCurrentSubStep()
		} else {
			dsp.mainSpinner = NewSpinner(dsp.writer, dsp.mainSteps[dsp.currentMain])
			dsp.mainSpinner.Start()
		}
	}
}

// NextSubStep moves to the next sub-step.
func (dsp *DetailedStepProgress) NextSubStep() {
	if dsp.subSpinner != nil {
		dsp.subSpinner.SuccessAndStop(fmt.Sprintf("  ✓ %s", dsp.subSteps[dsp.currentSub]))
	}

	dsp.currentSub++
	if dsp.currentSub < len(dsp.subSteps) {
		dsp.startCurrentSubStep()
	}
}

// NextMainStep moves to the next main step.
func (dsp *DetailedStepProgress) NextMainStep() {
	if dsp.subSpinner != nil {
		dsp.subSpinner.SuccessAndStop(fmt.Sprintf("  ✓ %s", dsp.subSteps[dsp.currentSub]))
	}
	dsp.currentMain++
}

// Fail marks the current step as failed.
func (dsp *DetailedStepProgress) Fail() {
	dsp.failed = true
	if dsp.subSpinner != nil {
		dsp.subSpinner.ErrorAndStop(fmt.Sprintf("  ✗ %s", dsp.subSteps[dsp.currentSub]))
	} else if dsp.mainSpinner != nil {
		dsp.mainSpinner.ErrorAndStop(fmt.Sprintf("Step %d/%d failed", dsp.currentMain+1, len(dsp.mainSteps)))
	}
	dsp.printSummary()
}

// Complete marks all steps as complete.
func (dsp *DetailedStepProgress) Complete() {
	if dsp.subSpinner != nil {
		dsp.subSpinner.SuccessAndStop(fmt.Sprintf("  ✓ %s", dsp.subSteps[dsp.currentSub]))
	} else if dsp.mainSpinner != nil {
		dsp.mainSpinner.SuccessAndStop(fmt.Sprintf("Step %d/%d completed", dsp.currentMain+1, len(dsp.mainSteps)))
	}
	dsp.currentMain = len(dsp.mainSteps)
	dsp.printSummary()
}

// startCurrentSubStep starts the spinner for the current sub-step.
func (dsp *DetailedStepProgress) startCurrentSubStep() {
	if dsp.currentSub < len(dsp.subSteps) {
		subMsg := fmt.Sprintf("  %s", dsp.subSteps[dsp.currentSub])
		dsp.subSpinner = NewSpinner(dsp.writer, subMsg)
		dsp.subSpinner.Start()
	}
}

// printHeader prints the header.
func (dsp *DetailedStepProgress) printHeader() {
	fmt.Fprintf(dsp.writer, "\n")
	fmt.Fprintf(dsp.writer, "%s\n", Bold(dsp.title))
	fmt.Fprintf(dsp.writer, "%s\n", Dim("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"))
}

// printSubStepsOverview prints an overview of sub-steps.
func (dsp *DetailedStepProgress) printSubStepsOverview() {
	for i, subStep := range dsp.subSteps {
		fmt.Fprintf(dsp.writer, "  %s %s\n", Dim("•"), Dim(subStep))
		_ = i // avoid unused variable warning
	}
	fmt.Fprintf(dsp.writer, "\n")
}

// printSummary prints the final summary.
func (dsp *DetailedStepProgress) printSummary() {
	elapsed := time.Since(dsp.startTime)
	fmt.Fprintf(dsp.writer, "\n")
	fmt.Fprintf(dsp.writer, "%s\n", Dim("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"))

	if dsp.failed {
		fmt.Fprintf(dsp.writer, "%s Process failed after %v\n", Error("❌"), elapsed.Round(time.Second))
		if dsp.currentMain < len(dsp.mainSteps) {
			fmt.Fprintf(dsp.writer, "%s Failed at step %d: %s\n", Error(""), dsp.currentMain+1, dsp.mainSteps[dsp.currentMain])
		}
	} else {
		fmt.Fprintf(dsp.writer, "%s Process completed successfully in %v\n", Success("✅"), elapsed.Round(time.Second))
		fmt.Fprintf(dsp.writer, "%s All %d steps completed\n", Success(""), len(dsp.mainSteps))
	}
	fmt.Fprintf(dsp.writer, "\n")
}
